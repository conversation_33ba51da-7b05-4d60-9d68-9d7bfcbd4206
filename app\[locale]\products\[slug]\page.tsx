import { notFound } from "next/navigation"
import { ProductDetail } from "./components/ProductDetail"
import { Metadata } from 'next'
import { productDetails } from "@/lib/products-data"
import { Breadcrumb } from "@/components/Breadcrumb"
import { generateBreadcrumbStructuredData, generateProductStructuredData } from "@/lib/seo-utils"

type Benefit = {
  title: string;
  description: string;
}

type Product = {
  name: string;
  description: string;
  features: string[];
  techSpecs: {
    deployment: string;
    security: string;
    availability: string;
    support: string;
  };
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
}

async function getProduct(slug: string): Promise<Product | null> {
  const product = productDetails[slug as keyof typeof productDetails]
  return product || null
}

async function getAllProductSlugs() {
  return Object.keys(productDetails)
}

export async function generateStaticParams() {
  const slugs = await getAllProductSlugs()
  return slugs.map((slug:any) => ({
    slug: slug,
  }))
}


export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  const product = await getProduct(params.slug)
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'

  if (!product) {
    return {
      title: '产品未找到 - 零点科技',
      description: '抱歉，您访问的产品页面不存在',
    }
  }

  return {
    title: `${product.name} - 零点科技`,
    description: `${product.description} 零点科技为您提供专业的${product.name}服务，助力企业数字化转型。`,
    keywords: [
      product.name,
      '零点科技',
      ...product.features,
      product.techSpecs?.deployment,
      product.techSpecs?.security,
    ].filter(Boolean),
    openGraph: {
      title: `${product.name} - 零点科技`,
      description: product.description,
      url: `${baseUrl}/products/${params.slug}`,
      siteName: '零点科技',
      images: [
        {
          url: `${baseUrl}/products/${params.slug}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: `${product.name} - 零点科技`,
        },
      ],
      locale: 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${product.name} - 零点科技`,
      description: product.description,
      images: [`${baseUrl}/products/${params.slug}/og-image.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/products/${params.slug}`,
    },
  }
}

export default async function ProductPage({ params }: { params: { slug: string } }) {
  const product = await getProduct(params.slug)

  if (!product) {
    notFound()
  }

  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '产品服务', href: '/products' },
    { label: product.name, href: `/products/${params.slug}` }
  ]

  const structuredData = {
    ...generateProductStructuredData(product, params.slug),
    "breadcrumb": generateBreadcrumbStructuredData(breadcrumbItems)
  }

  return (
    <>
      <div className="container mx-auto px-6 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      <ProductDetail product={product} />

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
    </>
  )
}