import { Metadata } from 'next'
import { ProductsClient } from "./components/ProductsClient"
import { products } from "@/lib/products-data"
import { Breadcrumb } from "@/components/Breadcrumb"
import { generateBreadcrumbStructuredData, generateProductListStructuredData } from "@/lib/seo-utils"

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com';

export const metadata: Metadata = {
  title: '产品服务 - 零点科技',
  description: '零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务。我们的产品覆盖AI服务、云计算、教育科技和定制开发四大领域，为企业提供全方位的技术解决方案。',
  keywords: [
    '零点科技产品',
    'AI智能标注服务',
    'CPU算力租用',
    '教育培训管理系统',
    '定制软件开发',
    '企业级解决方案',
    '云计算服务',
    '人工智能产品',
    '教育科技平台',
    '软件定制开发'
  ],
  openGraph: {
    title: '产品服务 - 零点科技',
    description: '零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务',
    url: `${baseUrl}/products`,
    siteName: '零点科技',
    images: [
      {
        url: `${baseUrl}/og-products.jpg`,
        width: 1200,
        height: 630,
        alt: '零点科技产品服务',
      },
    ],
    locale: 'zh_CN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '产品服务 - 零点科技',
    description: '零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务',
    images: [`${baseUrl}/og-products.jpg`],
  },
  alternates: {
    canonical: `${baseUrl}/products`,
  },
};

export default function Products() {
  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '产品服务', href: '/products' }
  ]

  const structuredData = {
    ...generateProductListStructuredData(products),
    "breadcrumb": generateBreadcrumbStructuredData(breadcrumbItems)
  }

  return (
    <>
      <div className="container mx-auto px-6 py-4">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      <ProductsClient products={products} />

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
    </>
  )
}