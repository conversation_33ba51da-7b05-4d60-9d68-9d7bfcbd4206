import { NextResponse } from 'next/server'
import {
  Brain, Cpu, GraduationCap, Target, Eye, Database,
  LineChart, Zap, Globe, CheckCircle2, Shield,
  Users, BookOpen, Award, BarChart, Clock, Settings
} from "lucide-react"

const products = [
  {
    slug: "ai-annotation",
    name: "AI智能标注平台",
    description: "专业的AI数据标注服务，支持图像、文本、语音等多模态数据标注，为机器学习模型提供高质量训练数据",
    iconName: "Brain",
    features: [
      { text: "图像标注", iconName: "Eye" },
      { text: "文本标注", iconName: "Database" },
      { text: "语音标注", iconName: "Target" },
      { text: "质量控制", iconName: "CheckCircle2" }
    ],
    highlight: "AI核心",
    price: "按标注量计费",
    category: "AI服务"
  },
  {
    slug: "cpu-rental",
    name: "CPU算力租用",
    description: "灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求",
    iconName: "Cpu",
    features: [
      { text: "高性能计算", iconName: "Zap" },
      { text: "弹性扩容", iconName: "Globe" },
      { text: "按需付费", iconName: "BarChart" },
      { text: "24/7监控", iconName: "Shield" }
    ],
    highlight: "最受欢迎",
    price: "¥0.5/核心/小时起",
    category: "云计算"
  },
  {
    slug: "education-management",
    name: "教育培训管理系统",
    description: "一站式教育培训管理平台，涵盖课程管理、学员管理、在线考试、证书颁发等完整教育生态",
    iconName: "GraduationCap",
    features: [
      { text: "课程管理", iconName: "BookOpen" },
      { text: "在线考试", iconName: "Target" },
      { text: "学员跟踪", iconName: "Users" },
      { text: "证书系统", iconName: "Award" }
    ],
    highlight: "教育专业",
    price: "¥299/月起",
    category: "教育科技"
  },
  {
    slug: "data-annotation-pro",
    name: "专业数据标注",
    description: "面向企业级客户的高精度数据标注服务，提供定制化标注方案和专业质检团队",
    iconName: "Database",
    features: [
      { text: "定制化方案", iconName: "Settings" },
      { text: "专业质检", iconName: "CheckCircle2" },
      { text: "批量处理", iconName: "Zap" },
      { text: "API接口", iconName: "Globe" }
    ],
    price: "定制报价",
    category: "AI服务"
  },
  {
    slug: "custom-web-development",
    name: "定制Web开发",
    description: "企业级Web应用开发服务，提供前后端全栈开发、系统架构设计和技术咨询",
    iconName: "Code",
    features: [
      { text: "全栈开发", iconName: "Globe" },
      { text: "响应式设计", iconName: "Monitor" },
      { text: "API开发", iconName: "Database" },
      { text: "性能优化", iconName: "Zap" }
    ],
    highlight: "热门推荐",
    price: "¥50,000起",
    category: "定制开发"
  },
  {
    slug: "mobile-app-development",
    name: "移动应用开发",
    description: "iOS和Android原生应用开发，以及跨平台移动应用解决方案",
    iconName: "Smartphone",
    features: [
      { text: "原生开发", iconName: "Smartphone" },
      { text: "跨平台", iconName: "Globe" },
      { text: "UI/UX设计", iconName: "Palette" },
      { text: "应用发布", iconName: "Upload" }
    ],
    price: "¥80,000起",
    category: "定制开发"
  },
  {
    slug: "enterprise-software",
    name: "企业软件定制",
    description: "面向企业的定制化软件解决方案，包括ERP、CRM、OA等管理系统",
    iconName: "Building",
    features: [
      { text: "需求分析", iconName: "Search" },
      { text: "系统设计", iconName: "Settings" },
      { text: "集成开发", iconName: "Code" },
      { text: "运维支持", iconName: "Shield" }
    ],
    price: "定制报价",
    category: "定制开发"
  }
]

export async function GET() {
  return NextResponse.json(products)
} 