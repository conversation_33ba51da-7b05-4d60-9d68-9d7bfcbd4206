'use client'

import React, { useContext, createContext, ReactNode } from 'react';
import type { Locale } from '@/lib/i18n';

// 翻译上下文
interface TranslationContextType {
  locale: Locale;
  messages: Record<string, any>;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const TranslationContext = createContext<TranslationContextType | null>(null);

// 翻译提供者组件
export function TranslationProvider({ 
  children, 
  locale, 
  messages 
}: { 
  children: ReactNode; 
  locale: Locale; 
  messages: Record<string, any>;
}): JSX.Element {
  const t = (key: string, params?: Record<string, string | number>): string => {
    const keys = key.split('.');
    let value: any = messages;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    if (typeof value !== 'string') {
      console.warn(`Translation value is not a string: ${key}`);
      return key;
    }
    
    // 简单的参数替换
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }
    
    return value;
  };

  return (
    <TranslationContext.Provider value={{ locale, messages, t }}>
      {children}
    </TranslationContext.Provider>
  );
}

// 使用翻译的hook
export function useTranslations(namespace?: string) {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslations must be used within a TranslationProvider');
  }
  
  const { t: baseT } = context;
  
  if (namespace) {
    return (key: string, params?: Record<string, string | number>) => {
      return baseT(`${namespace}.${key}`, params);
    };
  }
  
  return baseT;
}

// 获取当前语言
export function useLocale() {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useLocale must be used within a TranslationProvider');
  }
  
  return context.locale;
}
