// 服务器端SEO工具函数

interface BreadcrumbItem {
  label: string
  href: string
}

// 生成结构化数据的面包屑（服务器端版本）
export function generateBreadcrumbStructuredData(items: BreadcrumbItem[]) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `${baseUrl}${item.href}`
    }))
  }
}

// 生成产品结构化数据
export function generateProductStructuredData(product: any, slug: string) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "url": `${baseUrl}/products/${slug}`,
    "brand": {
      "@type": "Brand",
      "name": "零点科技"
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "零点科技"
    },
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "seller": {
        "@type": "Organization",
        "name": "零点科技"
      }
    },
    "additionalProperty": product.features?.map((feature: string) => ({
      "@type": "PropertyValue",
      "name": "功能特性",
      "value": feature
    }))
  }
}

// 生成产品列表结构化数据
export function generateProductListStructuredData(products: any[]) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "产品服务",
    "description": "零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务",
    "url": `${baseUrl}/products`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": products.length,
      "itemListElement": products.map((product, index) => ({
        "@type": "Product",
        "position": index + 1,
        "name": product.name,
        "description": product.description,
        "category": product.category,
        "url": `${baseUrl}/products/${product.slug}`
      }))
    }
  }
}

// 生成新闻文章结构化数据
export function generateNewsStructuredData(news: any) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    "headline": news.title,
    "description": news.summary || news.content?.substring(0, 160),
    "url": `${baseUrl}/news/${news.id}`,
    "datePublished": news.date,
    "dateModified": news.date,
    "author": {
      "@type": "Person",
      "name": news.author?.name || news.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "零点科技",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`
      }
    },
    "image": news.image,
    "articleSection": news.category
  }
}

// 生成页面结构化数据
export function generatePageStructuredData(page: {
  name: string
  description: string
  url: string
  breadcrumbs?: BreadcrumbItem[]
}) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  const structuredData: any = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": page.name,
    "description": page.description,
    "url": page.url,
    "mainEntity": {
      "@type": "Organization",
      "name": "零点科技",
      "description": "企业级AI与云计算解决方案提供商"
    }
  }
  
  if (page.breadcrumbs) {
    structuredData.breadcrumb = generateBreadcrumbStructuredData(page.breadcrumbs)
  }
  
  return structuredData
}
